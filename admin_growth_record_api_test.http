### 管理员成长记录管理API测试

# 设置变量
@baseUrl = http://localhost:3004
@adminToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEiLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6InN1cGVyX2FkbWluIiwiaXNBZG1pbiI6dHJ1ZSwiaWF0IjoxNzU2MDk4NDQ0LCJleHAiOjE3NTYxODQ4NDR9.h24ZJPj3qCQRcT_YPta0TztDgYmcry2ZAD53Dm4gOSU

### 1. 管理员登录（获取token）
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

### 2. 获取成长记录列表
GET {{baseUrl}}/admin/growth-records
Authorization: Bearer {{adminToken}}

### 3. 获取成长记录列表（带搜索）
GET {{baseUrl}}/admin/growth-records?search=测试&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 4. 获取成长记录列表（按等级筛选）
GET {{baseUrl}}/admin/growth-records?level=2&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 5. 获取成长记录列表（按多肉等级筛选）
GET {{baseUrl}}/admin/growth-records?plant_level=3&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 6. 获取用户成长记录详情
GET {{baseUrl}}/admin/growth-records/1
Authorization: Bearer {{adminToken}}

### 7. 更新用户等级
PUT {{baseUrl}}/admin/growth-records/1/update-level
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 5,
  "reason": "管理员手动调整等级"
}

### 8. 更新多肉能量值（增加）
PUT {{baseUrl}}/admin/growth-records/1/plants/1/update-energy
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "energy_change": 100,
  "reason": "管理员奖励能量"
}

### 9. 更新多肉能量值（减少）
PUT {{baseUrl}}/admin/growth-records/1/plants/1/update-energy
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "energy_change": -50,
  "reason": "管理员扣除能量"
}

### 10. 获取成长记录统计数据
GET {{baseUrl}}/admin/growth-records/statistics
Authorization: Bearer {{adminToken}}

### 11. 测试错误情况 - 用户不存在
GET {{baseUrl}}/admin/growth-records/99999
Authorization: Bearer {{adminToken}}

### 12. 测试错误情况 - 多肉不存在
PUT {{baseUrl}}/admin/growth-records/1/plants/99999/update-energy
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "energy_change": 50,
  "reason": "测试错误情况"
}

### 13. 测试错误情况 - 等级无效
PUT {{baseUrl}}/admin/growth-records/1/update-level
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 0,
  "reason": "测试无效等级"
}

### 14. 测试权限 - 无token
GET {{baseUrl}}/admin/growth-records

### 15. 测试权限 - 无效token
GET {{baseUrl}}/admin/growth-records
Authorization: Bearer invalid_token_here

### 16. 分页测试
GET {{baseUrl}}/admin/growth-records?page=1&limit=5
Authorization: Bearer {{adminToken}}

### 17. 复合筛选测试
GET {{baseUrl}}/admin/growth-records?search=用户&level=2&plant_level=3&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 18. 获取特定用户的详细信息（包含成就）
GET {{baseUrl}}/admin/growth-records/1
Authorization: Bearer {{adminToken}}

### 19. 批量操作测试 - 连续更新多个用户等级
PUT {{baseUrl}}/admin/growth-records/1/update-level
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 3,
  "reason": "批量调整-用户1"
}

###
PUT {{baseUrl}}/admin/growth-records/2/update-level
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 4,
  "reason": "批量调整-用户2"
}

### 20. 健康状态测试 - 查看不同健康状态的用户
GET {{baseUrl}}/admin/growth-records?page=1&limit=20
Authorization: Bearer {{adminToken}}

### 21. 统计数据详细查看
GET {{baseUrl}}/admin/growth-records/statistics
Authorization: Bearer {{adminToken}}

### 22. 成就系统测试 - 查看用户成就详情
GET {{baseUrl}}/admin/growth-records/1
Authorization: Bearer {{adminToken}}

### 23. 能量管理测试 - 大幅调整能量值
PUT {{baseUrl}}/admin/growth-records/1/plants/1/update-energy
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "energy_change": 500,
  "reason": "大幅奖励测试"
}

### 24. 负能量测试 - 确保能量不会变为负数
PUT {{baseUrl}}/admin/growth-records/1/plants/1/update-energy
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "energy_change": -10000,
  "reason": "负能量测试"
}
