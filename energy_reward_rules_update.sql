-- ========================================
-- 能量奖励规则表结构更新
-- ========================================

-- 检查表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS energy_reward_rules (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    rule_name VARCHAR(128) NOT NULL COMMENT '规则名称',
    rule_type ENUM('meditation_complete', 'daily_streak', 'level_up', 'special_event') NOT NULL COMMENT '规则类型',
    `condition` TEXT DEFAULT NULL COMMENT '触发条件（JSON格式）',
    energy_amount INT DEFAULT 0 COMMENT '奖励能量值',
    bonus_multiplier DECIMAL(3,2) DEFAULT 1.00 COMMENT '奖励倍数',
    max_daily_times INT DEFAULT 0 COMMENT '每日最大触发次数（0为无限制）',
    description TEXT DEFAULT NULL COMMENT '规则描述',
    priority INT DEFAULT 0 COMMENT '优先级（数字越大优先级越高）',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NULL DEFAULT NULL COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='能量奖励规则表';

-- 插入默认能量奖励规则（如果表为空）
INSERT IGNORE INTO energy_reward_rules (rule_name, rule_type, `condition`, energy_amount, bonus_multiplier, max_daily_times, description, priority, is_active) VALUES
('完成冥想任务', 'meditation_complete', '{"min_duration": 300}', 20, 1.0, 0, '完成5分钟以上冥想获得基础奖励', 1, TRUE),
('长时间冥想', 'meditation_complete', '{"min_duration": 1800}', 50, 1.5, 3, '完成30分钟冥想获得额外奖励', 2, TRUE),
('连续冥想3天', 'daily_streak', '{"streak_days": 3}', 100, 1.0, 1, '连续冥想3天获得奖励', 5, TRUE),
('连续冥想7天', 'daily_streak', '{"streak_days": 7}', 300, 1.0, 1, '连续冥想7天获得大量奖励', 8, TRUE),
('等级提升', 'level_up', '{}', 200, 1.0, 0, '用户等级提升时获得奖励', 10, TRUE),
('特殊活动奖励', 'special_event', '{"event_type": "holiday"}', 150, 2.0, 1, '节日特殊活动奖励', 3, FALSE);

-- 创建索引以提高查询性能（如果索引不存在）
-- CREATE INDEX idx_energy_reward_rules_type ON energy_reward_rules(rule_type);
-- CREATE INDEX idx_energy_reward_rules_active ON energy_reward_rules(is_active);
-- CREATE INDEX idx_energy_reward_rules_priority ON energy_reward_rules(priority);

-- 查看表结构
-- DESCRIBE energy_reward_rules;

-- 查看数据
-- SELECT * FROM energy_reward_rules ORDER BY priority DESC, created_at DESC;
