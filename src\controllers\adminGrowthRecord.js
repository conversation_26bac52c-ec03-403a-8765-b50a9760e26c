import { Op } from 'sequelize'
import { parsePaginationParams, formatPaginationResponse } from '../tool/Common'
import User from '../models/User'
import Plant from '../models/Plant'
import PlantGrowthRecord from '../models/PlantGrowthRecord'
import UserMeditationStats from '../models/UserMeditationStats'
import PlantLevelConfig from '../models/PlantLevelConfig'
import UserLevelConfig from '../models/UserLevelConfig'
import Achievement from '../models/Achievement'
import UserAchievement from '../models/UserAchievement'
import sequelize from '../lib/sequelize'

export default class AdminGrowthRecordController {
  /**
   * @swagger
   * /admin/growth-records:
   *   get:
   *     tags:
   *       - 成长记录管理
   *     summary: 获取用户成长记录列表
   *     description: 获取用户成长记录列表，包含用户信息、多肉信息、等级经验、属性值、成长天数、健康状态等
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: 每页数量
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: 搜索关键词（用户昵称）
   *       - in: query
   *         name: level
   *         schema:
   *           type: integer
   *         description: 筛选用户等级
   *       - in: query
   *         name: plant_level
   *         schema:
   *           type: integer
   *         description: 筛选多肉等级
   *     responses:
   *       200:
   *         description: 获取成功
   */
  static async getGrowthRecordList(ctx) {
    const { search, level, plant_level } = ctx.query

    try {
      const { pageNum, pageSize, offset } = parsePaginationParams(ctx.query)
      
      // 构建查询条件
      const userWhereCondition = {}
      const plantWhereCondition = {}

      if (search) {
        userWhereCondition.nickname = { [Op.like]: `%${search}%` }
      }

      if (level) {
        userWhereCondition.meditation_level = level
      }

      if (plant_level) {
        plantWhereCondition.level = plant_level
      }

      // 查询用户及其多肉信息
      const users = await User.findAndCountAll({
        where: userWhereCondition,
        include: [
          {
            model: Plant,
            as: 'plants',
            where: Object.keys(plantWhereCondition).length > 0 ? plantWhereCondition : undefined,
            required: false,
            include: [
              {
                model: PlantGrowthRecord,
                as: 'growth_records',
                limit: 5,
                order: [['created_at', 'DESC']]
              }
            ]
          },
          {
            model: UserMeditationStats,
            as: 'meditation_stats',
            where: {
              period_type: 'day'
            },
            required: false,
            limit: 30,
            order: [['period_date', 'DESC']]
          }
        ],
        limit: pageSize,
        offset: offset,
        order: [['created_at', 'DESC']],
        distinct: true
      })

      // 处理数据，计算成长天数、健康状态等
      const processedData = await Promise.all(users.rows.map(async (user) => {
        const userData = user.toJSON()
        
        // 计算成长天数（从创建时间到现在）
        const growthDays = Math.floor((new Date() - new Date(userData.created_at)) / (1000 * 60 * 60 * 24))
        
        // 计算总能量值和平均等级
        let totalEnergy = 0
        let avgPlantLevel = 0
        let healthStatus = 'healthy' // 默认健康
        let lastCareTime = null

        if (userData.plants && userData.plants.length > 0) {
          totalEnergy = userData.plants.reduce((sum, plant) => sum + plant.energy_value, 0)
          avgPlantLevel = userData.plants.reduce((sum, plant) => sum + plant.level, 0) / userData.plants.length
          
          // 获取最后照料时间（最近的成长记录时间）
          const allGrowthRecords = userData.plants.flatMap(plant => plant.growth_records || [])
          if (allGrowthRecords.length > 0) {
            lastCareTime = allGrowthRecords.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0].created_at
            
            // 判断健康状态：超过7天没有照料记录则为不健康
            const daysSinceLastCare = Math.floor((new Date() - new Date(lastCareTime)) / (1000 * 60 * 60 * 24))
            if (daysSinceLastCare > 7) {
              healthStatus = 'unhealthy'
            } else if (daysSinceLastCare > 3) {
              healthStatus = 'warning'
            }
          } else {
            healthStatus = 'no_care'
          }
        }

        // 计算总冥想时长和获得的总能量
        let totalMeditationDuration = 0
        let totalEnergyGained = 0
        if (userData.meditation_stats && userData.meditation_stats.length > 0) {
          totalMeditationDuration = userData.meditation_stats.reduce((sum, stat) => sum + stat.meditation_duration, 0)
          totalEnergyGained = userData.meditation_stats.reduce((sum, stat) => sum + stat.energy_gained, 0)
        }

        // 获取用户成就
        const userAchievements = await UserAchievement.findAll({
          where: { user_id: userData.id },
          include: [
            {
              model: Achievement,
              as: 'achievement'
            }
          ]
        })

        const achievements = userAchievements.map(ua => ({
          id: ua.achievement.id,
          name: ua.achievement.name,
          icon: ua.achievement.icon,
          category: ua.achievement.category,
          rarity: ua.achievement.rarity,
          unlocked_at: ua.unlocked_at,
          is_claimed: ua.is_claimed
        }))

        return {
          ...userData,
          growth_days: growthDays,
          total_energy: totalEnergy,
          avg_plant_level: Math.round(avgPlantLevel * 10) / 10,
          health_status: healthStatus,
          last_care_time: lastCareTime,
          total_meditation_duration: totalMeditationDuration,
          total_energy_gained: totalEnergyGained,
          achievements: achievements,
          plant_count: userData.plants ? userData.plants.length : 0
        }
      }))

      ctx.body = {
        code: 200,
        data: formatPaginationResponse(processedData, users.count, pageNum, pageSize)
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取成长记录列表失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/growth-records/{userId}:
   *   get:
   *     tags:
   *       - 成长记录管理
   *     summary: 获取用户成长记录详情
   *     description: 获取指定用户的详细成长记录信息
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: userId
   *         required: true
   *         schema:
   *           type: integer
   *         description: 用户ID
   *     responses:
   *       200:
   *         description: 获取成功
   *       404:
   *         description: 用户不存在
   */
  static async getGrowthRecordDetail(ctx) {
    const { userId } = ctx.params

    try {
      const user = await User.findByPk(userId, {
        include: [
          {
            model: Plant,
            as: 'plants',
            include: [
              {
                model: PlantGrowthRecord,
                as: 'growth_records',
                order: [['created_at', 'DESC']]
              }
            ]
          },
          {
            model: UserMeditationStats,
            as: 'meditation_stats',
            order: [['period_date', 'DESC']]
          }
        ]
      })

      if (!user) {
        ctx.body = {
          code: 404,
          message: '用户不存在'
        }
        return
      }

      const userData = user.toJSON()
      
      // 计算详细统计信息
      const growthDays = Math.floor((new Date() - new Date(userData.created_at)) / (1000 * 60 * 60 * 24))
      
      // 多肉统计
      const plantStats = {
        total_count: userData.plants ? userData.plants.length : 0,
        total_energy: userData.plants ? userData.plants.reduce((sum, plant) => sum + plant.energy_value, 0) : 0,
        avg_level: userData.plants && userData.plants.length > 0 
          ? userData.plants.reduce((sum, plant) => sum + plant.level, 0) / userData.plants.length 
          : 0,
        max_level: userData.plants && userData.plants.length > 0 
          ? Math.max(...userData.plants.map(plant => plant.level)) 
          : 0
      }

      // 冥想统计
      const meditationStats = {
        total_duration: userData.meditation_stats 
          ? userData.meditation_stats.reduce((sum, stat) => sum + stat.meditation_duration, 0) 
          : 0,
        total_energy_gained: userData.meditation_stats 
          ? userData.meditation_stats.reduce((sum, stat) => sum + stat.energy_gained, 0) 
          : 0,
        total_tasks_completed: userData.meditation_stats 
          ? userData.meditation_stats.reduce((sum, stat) => sum + stat.tasks_completed, 0) 
          : 0
      }

      // 健康状态分析
      let healthStatus = 'healthy'
      let lastCareTime = null
      if (userData.plants && userData.plants.length > 0) {
        const allGrowthRecords = userData.plants.flatMap(plant => plant.growth_records || [])
        if (allGrowthRecords.length > 0) {
          lastCareTime = allGrowthRecords.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0].created_at
          const daysSinceLastCare = Math.floor((new Date() - new Date(lastCareTime)) / (1000 * 60 * 60 * 24))
          if (daysSinceLastCare > 7) {
            healthStatus = 'unhealthy'
          } else if (daysSinceLastCare > 3) {
            healthStatus = 'warning'
          }
        } else {
          healthStatus = 'no_care'
        }
      }

      // 获取用户成就详情
      const userAchievements = await UserAchievement.findAll({
        where: { user_id: userId },
        include: [
          {
            model: Achievement,
            as: 'achievement'
          }
        ],
        order: [['unlocked_at', 'DESC']]
      })

      const achievements = userAchievements.map(ua => ({
        id: ua.achievement.id,
        name: ua.achievement.name,
        description: ua.achievement.description,
        icon: ua.achievement.icon,
        category: ua.achievement.category,
        rarity: ua.achievement.rarity,
        unlocked_at: ua.unlocked_at,
        is_claimed: ua.is_claimed,
        claimed_at: ua.claimed_at,
        progress: ua.progress
      }))

      ctx.body = {
        code: 200,
        data: {
          user_info: userData,
          growth_days: growthDays,
          plant_stats: plantStats,
          meditation_stats: meditationStats,
          health_status: healthStatus,
          last_care_time: lastCareTime,
          achievements: achievements
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取成长记录详情失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/growth-records/{userId}/update-level:
   *   put:
   *     tags:
   *       - 成长记录管理
   *     summary: 更新用户等级
   *     description: 手动更新用户的冥想等级
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: userId
   *         required: true
   *         schema:
   *           type: integer
   *         description: 用户ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - level
   *             properties:
   *               level:
   *                 type: integer
   *                 description: 新的等级
   *               reason:
   *                 type: string
   *                 description: 更新原因
   *     responses:
   *       200:
   *         description: 更新成功
   *       404:
   *         description: 用户不存在
   */
  static async updateUserLevel(ctx) {
    const { userId } = ctx.params
    const { level, reason } = ctx.request.body

    if (!level || level < 1) {
      ctx.body = {
        code: 400,
        message: '等级必须大于0'
      }
      return
    }

    try {
      const user = await User.findByPk(userId)
      if (!user) {
        ctx.body = {
          code: 404,
          message: '用户不存在'
        }
        return
      }

      const oldLevel = user.meditation_level
      await user.update({
        meditation_level: level,
        updated_at: new Date()
      })

      ctx.body = {
        code: 200,
        message: '用户等级更新成功',
        data: {
          user_id: userId,
          old_level: oldLevel,
          new_level: level,
          reason: reason || '管理员手动调整'
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '更新用户等级失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/growth-records/{userId}/plants/{plantId}/update-energy:
   *   put:
   *     tags:
   *       - 成长记录管理
   *     summary: 更新多肉能量值
   *     description: 手动更新多肉的能量值并记录成长记录
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: userId
   *         required: true
   *         schema:
   *           type: integer
   *         description: 用户ID
   *       - in: path
   *         name: plantId
   *         required: true
   *         schema:
   *           type: integer
   *         description: 多肉ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - energy_change
   *             properties:
   *               energy_change:
   *                 type: integer
   *                 description: 能量值变化（可为负数）
   *               reason:
   *                 type: string
   *                 description: 变化原因
   *     responses:
   *       200:
   *         description: 更新成功
   *       404:
   *         description: 多肉不存在
   */
  static async updatePlantEnergy(ctx) {
    const { userId, plantId } = ctx.params
    const { energy_change, reason } = ctx.request.body

    if (energy_change === undefined || energy_change === null) {
      ctx.body = {
        code: 400,
        message: '能量值变化不能为空'
      }
      return
    }

    try {
      const plant = await Plant.findOne({
        where: {
          id: plantId,
          user_id: userId
        }
      })

      if (!plant) {
        ctx.body = {
          code: 404,
          message: '多肉不存在'
        }
        return
      }

      const oldEnergy = plant.energy_value
      const newEnergy = Math.max(0, oldEnergy + energy_change)

      // 更新多肉能量值
      await plant.update({
        energy_value: newEnergy,
        updated_at: new Date()
      })

      // 记录成长记录
      await PlantGrowthRecord.create({
        plant_id: plantId,
        change_value: energy_change,
        reason: reason || '管理员手动调整'
      })

      ctx.body = {
        code: 200,
        message: '多肉能量值更新成功',
        data: {
          plant_id: plantId,
          old_energy: oldEnergy,
          new_energy: newEnergy,
          change_value: energy_change,
          reason: reason || '管理员手动调整'
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '更新多肉能量值失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/growth-records/statistics:
   *   get:
   *     tags:
   *       - 成长记录管理
   *     summary: 获取成长记录统计数据
   *     description: 获取整体的成长记录统计数据
   *     security:
   *       - AdminBearer: []
   *     responses:
   *       200:
   *         description: 获取成功
   */
  static async getGrowthStatistics(ctx) {
    try {
      // 用户统计
      const userStats = await User.findAll({
        attributes: [
          [sequelize.fn('COUNT', sequelize.col('id')), 'total_users'],
          [sequelize.fn('AVG', sequelize.col('meditation_level')), 'avg_level'],
          [sequelize.fn('AVG', sequelize.col('streak_days')), 'avg_streak_days'],
          [sequelize.fn('MAX', sequelize.col('streak_days')), 'max_streak_days']
        ],
        raw: true
      })

      // 多肉统计
      const plantStats = await Plant.findAll({
        attributes: [
          [sequelize.fn('COUNT', sequelize.col('id')), 'total_plants'],
          [sequelize.fn('AVG', sequelize.col('energy_value')), 'avg_energy'],
          [sequelize.fn('AVG', sequelize.col('level')), 'avg_level'],
          [sequelize.fn('MAX', sequelize.col('level')), 'max_level']
        ],
        raw: true
      })

      // 成长记录统计
      const growthRecordStats = await PlantGrowthRecord.findAll({
        attributes: [
          [sequelize.fn('COUNT', sequelize.col('id')), 'total_records'],
          [sequelize.fn('SUM', sequelize.col('change_value')), 'total_energy_change']
        ],
        raw: true
      })

      // 等级分布
      const levelDistribution = await User.findAll({
        attributes: [
          'meditation_level',
          [sequelize.fn('COUNT', sequelize.col('id')), 'user_count']
        ],
        group: ['meditation_level'],
        order: [['meditation_level', 'ASC']],
        raw: true
      })

      // 健康状态统计
      const healthStatusStats = await AdminGrowthRecordController.calculateHealthStatusStats()

      ctx.body = {
        code: 200,
        data: {
          user_stats: userStats[0],
          plant_stats: plantStats[0],
          growth_record_stats: growthRecordStats[0],
          level_distribution: levelDistribution,
          health_status_stats: healthStatusStats
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取统计数据失败',
        error: error.message
      }
    }
  }

  // 辅助方法：计算健康状态统计
  static async calculateHealthStatusStats() {
    try {
      const users = await User.findAll({
        include: [
          {
            model: Plant,
            as: 'plants',
            include: [
              {
                model: PlantGrowthRecord,
                as: 'growth_records',
                limit: 1,
                order: [['created_at', 'DESC']]
              }
            ]
          }
        ]
      })

      const healthStats = {
        healthy: 0,
        warning: 0,
        unhealthy: 0,
        no_care: 0
      }

      users.forEach(user => {
        if (!user.plants || user.plants.length === 0) {
          healthStats.no_care++
          return
        }

        const allGrowthRecords = user.plants.flatMap(plant => plant.growth_records || [])
        if (allGrowthRecords.length === 0) {
          healthStats.no_care++
          return
        }

        const lastCareTime = allGrowthRecords.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))[0].created_at
        const daysSinceLastCare = Math.floor((new Date() - new Date(lastCareTime)) / (1000 * 60 * 60 * 24))

        if (daysSinceLastCare > 7) {
          healthStats.unhealthy++
        } else if (daysSinceLastCare > 3) {
          healthStats.warning++
        } else {
          healthStats.healthy++
        }
      })

      return healthStats
    } catch (error) {
      console.error('计算健康状态统计失败:', error)
      return { healthy: 0, warning: 0, unhealthy: 0, no_care: 0 }
    }
  }
}
