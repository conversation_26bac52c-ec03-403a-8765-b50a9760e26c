import { DataTypes } from 'sequelize'
import sequelize from '../lib/sequelize'

const Achievement = sequelize.define('Achievement', {
  id: {
    type: DataTypes.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  name: {
    type: DataTypes.STRING(128),
    allowNull: false,
    comment: '成就名称'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '成就描述'
  },
  icon: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: '成就图标'
  },
  category: {
    type: DataTypes.STRING(20),
    allowNull: false,
    comment: '成就类别：meditation/plant/streak/level/special'
  },
  condition_type: {
    type: DataTypes.STRING(30),
    allowNull: false,
    comment: '触发条件类型'
  },
  condition_value: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '触发条件值'
  },
  condition_config: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '额外条件配置（JSON格式）'
  },
  reward_type: {
    type: DataTypes.STRING(20),
    defaultValue: 'badge',
    comment: '奖励类型：energy/title/badge/special'
  },
  reward_value: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '奖励值'
  },
  reward_config: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '奖励配置（JSON格式）'
  },
  rarity: {
    type: DataTypes.STRING(20),
    defaultValue: 'common',
    comment: '稀有度：common/rare/epic/legendary'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: '是否启用'
  },
  sort_order: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: '排序顺序'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: '创建时间'
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '更新时间'
  }
}, {
  tableName: 'achievements',
  timestamps: false,
  comment: '成就配置表'
})

export default Achievement
