### 连接测试文件
### 用于排查网络连接问题

### 1. 基础连接测试
GET http://localhost:3004

### 2. 管理员登录测试 - 直接URL
POST http://localhost:3004/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

### 3. 管理员登录测试 - 带User-Agent
POST http://localhost:3004/admin/login
Content-Type: application/json
User-Agent: REST Client

{
  "username": "admin",
  "password": "admin123"
}

### 4. 管理员登录测试 - 禁用SSL验证（如果适用）
# @no-cookie-jar
# @no-redirect
POST http://localhost:3004/admin/login
Content-Type: application/json
Accept: application/json

{
  "username": "admin",
  "password": "admin123"
}

### 5. 健康检查
GET http://localhost:3004/health

### 6. API根路径测试
GET http://localhost:3004/api

### 7. 测试OPTIONS请求
OPTIONS http://localhost:3004/admin/login
