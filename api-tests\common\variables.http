### 全局变量配置文件
### 所有API测试文件都会引用这里定义的变量

# 服务器配置
@baseUrl = http://localhost:3004
@apiUrl = http://localhost:3004/api

# 管理员认证信息
@adminUsername = admin 
@adminPassword = admin123
@adminToken = 

# 测试用户信息
@testUserId = 1
@testUserOpenId = test_openid_123
@testUserToken = 

# 测试数据ID
@testMeditationId = 1
@testPlantId = 1
@testPlanId = 1
@testTagId = 1
@testLevelId = 1

# 分页参数
@defaultPage = 1
@defaultLimit = 10
@maxLimit = 100

# 文件上传
@uploadPath = /uploads/test
@testImageFile = test-image.jpg

# 微信相关
@testWxCode = test_wx_code_123
@testWxAppId = your_wx_app_id
@testWxAppSecret = your_wx_app_secret

# 测试环境标识
@environment = development
@debugMode = true
