### 冥想内容管理接口测试
### 引用全局变量
< ../common/variables.http

### 前置条件：需要先获取管理员token
< ../auth/admin-auth.http

### 1. 获取冥想内容列表 - 默认分页
GET {{baseUrl}}/admin/meditation/contents?page={{defaultPage}}&limit={{defaultLimit}}
Authorization: Bearer {{adminToken}}

### 2. 获取冥想内容列表 - 搜索功能
GET {{baseUrl}}/admin/meditation/contents?search=冥想&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 3. 获取冥想内容列表 - 按状态筛选
GET {{baseUrl}}/admin/meditation/contents?status=published&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 4. 获取冥想内容列表 - 按难度筛选
GET {{baseUrl}}/admin/meditation/contents?difficulty=beginner&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 5. 获取冥想内容列表 - 按类型筛选
GET {{baseUrl}}/admin/meditation/contents?type=guided&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 6. 获取冥想内容列表 - 按标签筛选
GET {{baseUrl}}/admin/meditation/contents?tag_id={{testTagId}}&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 7. 获取冥想内容列表 - 按时长筛选
GET {{baseUrl}}/admin/meditation/contents?min_duration=5&max_duration=15&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 8. 获取冥想内容列表 - 复合筛选
GET {{baseUrl}}/admin/meditation/contents?search=放松&status=published&difficulty=beginner&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 9. 获取冥想内容详情 - 正常内容
GET {{baseUrl}}/admin/meditation/contents/{{testMeditationId}}
Authorization: Bearer {{adminToken}}

### 10. 获取冥想内容详情 - 不存在的内容
GET {{baseUrl}}/admin/meditation/contents/99999
Authorization: Bearer {{adminToken}}

### 11. 创建冥想内容 - 完整信息
POST {{baseUrl}}/admin/meditation/contents
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "title": "晨间正念冥想",
  "description": "帮助你开始美好一天的正念冥想练习",
  "content": "深呼吸，感受当下的宁静...",
  "type": "guided",
  "difficulty": "beginner",
  "duration": 10,
  "audio_url": "https://example.com/audio/morning-meditation.mp3",
  "image_url": "https://example.com/images/morning-meditation.jpg",
  "tags": [1, 2, 3],
  "status": "draft",
  "sort_order": 1,
  "is_featured": true,
  "metadata": {
    "instructor": "张老师",
    "background_music": "自然音效",
    "language": "中文"
  }
}

### 12. 创建冥想内容 - 最小信息
POST {{baseUrl}}/admin/meditation/contents
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "title": "简单冥想",
  "description": "基础冥想练习",
  "content": "专注呼吸...",
  "type": "guided",
  "difficulty": "beginner",
  "duration": 5
}

### 13. 创建冥想内容 - 缺少必填字段
POST {{baseUrl}}/admin/meditation/contents
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "description": "缺少标题的内容",
  "content": "测试内容"
}

### 14. 创建冥想内容 - 无效类型
POST {{baseUrl}}/admin/meditation/contents
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "title": "无效类型测试",
  "description": "测试无效类型",
  "content": "测试内容",
  "type": "invalid_type",
  "difficulty": "beginner",
  "duration": 5
}

### 15. 创建冥想内容 - 无效难度
POST {{baseUrl}}/admin/meditation/contents
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "title": "无效难度测试",
  "description": "测试无效难度",
  "content": "测试内容",
  "type": "guided",
  "difficulty": "invalid_difficulty",
  "duration": 5
}

### 16. 创建冥想内容 - 负数时长
POST {{baseUrl}}/admin/meditation/contents
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "title": "负数时长测试",
  "description": "测试负数时长",
  "content": "测试内容",
  "type": "guided",
  "difficulty": "beginner",
  "duration": -5
}

### 17. 更新冥想内容 - 正常更新
PUT {{baseUrl}}/admin/meditation/contents/{{testMeditationId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "title": "更新后的冥想标题",
  "description": "更新后的描述",
  "status": "published"
}

### 18. 更新冥想内容 - 更新标签
PUT {{baseUrl}}/admin/meditation/contents/{{testMeditationId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "tags": [1, 3, 5]
}

### 19. 更新冥想内容 - 更新元数据
PUT {{baseUrl}}/admin/meditation/contents/{{testMeditationId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "metadata": {
    "instructor": "李老师",
    "background_music": "钢琴音乐",
    "language": "中文",
    "updated_reason": "内容优化"
  }
}

### 20. 更新冥想内容 - 不存在的内容
PUT {{baseUrl}}/admin/meditation/contents/99999
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "title": "不存在的内容"
}

### 21. 发布冥想内容 - 发布草稿
PUT {{baseUrl}}/admin/meditation/contents/{{testMeditationId}}/publish
Authorization: Bearer {{adminToken}}

### 22. 发布冥想内容 - 不存在的内容
PUT {{baseUrl}}/admin/meditation/contents/99999/publish
Authorization: Bearer {{adminToken}}

### 23. 下架冥想内容 - 下架已发布内容
PUT {{baseUrl}}/admin/meditation/contents/{{testMeditationId}}/unpublish
Authorization: Bearer {{adminToken}}

### 24. 下架冥想内容 - 不存在的内容
PUT {{baseUrl}}/admin/meditation/contents/99999/unpublish
Authorization: Bearer {{adminToken}}

### 25. 删除冥想内容 - 软删除
DELETE {{baseUrl}}/admin/meditation/contents/{{testMeditationId}}
Authorization: Bearer {{adminToken}}

### 26. 删除冥想内容 - 不存在的内容
DELETE {{baseUrl}}/admin/meditation/contents/99999
Authorization: Bearer {{adminToken}}

### 27. 恢复冥想内容 - 恢复已删除内容
PUT {{baseUrl}}/admin/meditation/contents/{{testMeditationId}}/restore
Authorization: Bearer {{adminToken}}

### 28. 恢复冥想内容 - 不存在的内容
PUT {{baseUrl}}/admin/meditation/contents/99999/restore
Authorization: Bearer {{adminToken}}

### 29. 批量操作 - 批量发布
PUT {{baseUrl}}/admin/meditation/contents/batch/publish
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "ids": [1, 2, 3]
}

### 30. 批量操作 - 批量下架
PUT {{baseUrl}}/admin/meditation/contents/batch/unpublish
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "ids": [1, 2, 3]
}

### 31. 批量操作 - 批量删除
DELETE {{baseUrl}}/admin/meditation/contents/batch
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "ids": [4, 5, 6]
}

### 32. 批量操作 - 批量更新标签
PUT {{baseUrl}}/admin/meditation/contents/batch/tags
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "ids": [1, 2, 3],
  "tags": [1, 2],
  "action": "add"
}

### 33. 获取内容统计 - 基础统计
GET {{baseUrl}}/admin/meditation/contents/statistics
Authorization: Bearer {{adminToken}}

### 34. 获取内容统计 - 按状态分组
GET {{baseUrl}}/admin/meditation/contents/statistics?group_by=status
Authorization: Bearer {{adminToken}}

### 35. 获取内容统计 - 按类型分组
GET {{baseUrl}}/admin/meditation/contents/statistics?group_by=type
Authorization: Bearer {{adminToken}}

### 36. 获取内容统计 - 按难度分组
GET {{baseUrl}}/admin/meditation/contents/statistics?group_by=difficulty
Authorization: Bearer {{adminToken}}

### 37. 获取内容统计 - 按时间范围
GET {{baseUrl}}/admin/meditation/contents/statistics?start_date=2024-01-01&end_date=2024-12-31
Authorization: Bearer {{adminToken}}

### 38. 内容审核 - 审核通过
PUT {{baseUrl}}/admin/meditation/contents/{{testMeditationId}}/approve
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "review_notes": "内容质量良好，审核通过"
}

### 39. 内容审核 - 审核拒绝
PUT {{baseUrl}}/admin/meditation/contents/{{testMeditationId}}/reject
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "review_notes": "内容需要进一步完善",
  "rejection_reason": "content_quality"
}

### 40. 内容排序 - 更新排序
PUT {{baseUrl}}/admin/meditation/contents/{{testMeditationId}}/sort
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "sort_order": 10
}

### 41. 内容推荐 - 设为推荐
PUT {{baseUrl}}/admin/meditation/contents/{{testMeditationId}}/feature
Authorization: Bearer {{adminToken}}

### 42. 内容推荐 - 取消推荐
PUT {{baseUrl}}/admin/meditation/contents/{{testMeditationId}}/unfeature
Authorization: Bearer {{adminToken}}

### 43. 权限测试 - 无token访问
GET {{baseUrl}}/admin/meditation/contents

### 44. 权限测试 - 无效token访问
GET {{baseUrl}}/admin/meditation/contents
Authorization: Bearer invalid_token

### 45. 权限测试 - 普通管理员权限
# 注意：需要普通管理员token
GET {{baseUrl}}/admin/meditation/contents
Authorization: Bearer normal_admin_token_here

### 46. 性能测试 - 大量数据查询
GET {{baseUrl}}/admin/meditation/contents?page=1&limit=100
Authorization: Bearer {{adminToken}}

### 47. 数据验证测试 - 超长标题
POST {{baseUrl}}/admin/meditation/contents
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "title": "超长标题测试".repeat(50),
  "description": "测试超长标题",
  "content": "测试内容",
  "type": "guided",
  "difficulty": "beginner",
  "duration": 5
}

### 48. 数据验证测试 - 特殊字符
POST {{baseUrl}}/admin/meditation/contents
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "title": "特殊字符@#$%^&*()",
  "description": "测试特殊字符<script>alert('xss')</script>",
  "content": "测试内容",
  "type": "guided",
  "difficulty": "beginner",
  "duration": 5
}

### 49. 导出功能测试 - 导出内容列表
GET {{baseUrl}}/admin/meditation/contents/export?format=csv&status=published
Authorization: Bearer {{adminToken}}

### 50. 导入功能测试 - 批量导入内容
POST {{baseUrl}}/admin/meditation/contents/import
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "contents": [
    {
      "title": "导入测试1",
      "description": "批量导入测试内容1",
      "content": "测试内容1",
      "type": "guided",
      "difficulty": "beginner",
      "duration": 5
    },
    {
      "title": "导入测试2",
      "description": "批量导入测试内容2",
      "content": "测试内容2",
      "type": "music",
      "difficulty": "intermediate",
      "duration": 10
    }
  ]
}
